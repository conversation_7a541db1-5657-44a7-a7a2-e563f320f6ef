# Trending Products Data Pipeline Plan

## Data Schema Analysis Summary

### Key Tables & Their Value:

**Tier 1 - Core Engagement (Highest ROI)**
- `user_searched_products` (1.79M rows) - Contains: search queries, filters, results, user context
- `product_result_shown` (1.35M rows) - Contains: product_id, URL, price, brand (scraped_data), user location
- `text_search` (313K rows) - Contains: raw search queries, user context
- `search_success` (76K rows) - Contains: successful search results, filters applied
- `product_result_clicked` (50K rows) - Contains: clicked products, prices, brands, user engagement
- `top_results_clicked` (60K rows) - Contains: product details, prices, brands, context

**Tier 2 - Engagement Signals**  
- `item_favorited` (12K rows) - Contains: product_id, user preferences
- `view_similar_items_clicked` (51K rows) - Contains: related product interest, brands, prices

## Phase 1: Core Data Extraction (Week 1-2)

### 1.1 Product Trending Metrics Tables
Create these Supabase tables to store processed insights:

```sql
-- Daily product trending metrics
CREATE TABLE daily_product_trends (
  date DATE,
  product_id VARCHAR,
  product_url VARCHAR,
  brand VARCHAR,
  name VARCHAR,
  price_numeric FLOAT,
  
  -- Engagement metrics
  search_appearances INTEGER DEFAULT 0,
  search_clicks INTEGER DEFAULT 0,
  product_views INTEGER DEFAULT 0,
  favorites INTEGER DEFAULT 0,
  similar_item_clicks INTEGER DEFAULT 0,
  
  -- Performance metrics
  click_through_rate FLOAT,
  favorite_rate FLOAT,
  trend_velocity FLOAT,
  
  -- Context
  top_search_queries JSONB,
  user_regions JSONB,
  platforms JSONB,
  
  PRIMARY KEY (date, product_id)
);

-- Search trends
CREATE TABLE daily_search_trends (
  date DATE,
  search_query VARCHAR,
  search_count INTEGER,
  success_rate FLOAT,
  top_clicked_products JSONB,
  user_regions JSONB,
  PRIMARY KEY (date, search_query)
);

-- Brand trends  
CREATE TABLE daily_brand_trends (
  date DATE,
  brand VARCHAR,
  search_count INTEGER,
  product_clicks INTEGER,
  favorites INTEGER,
  avg_price FLOAT,
  trending_products JSONB,
  PRIMARY KEY (date, brand)
);
```

### 1.2 Core DAG Queries

**Query 1: Daily Product Engagement Aggregation**
```sql
-- Aggregate daily product metrics from multiple sources
WITH product_searches AS (
  SELECT 
    DATE(time) as date,
    JSON_EXTRACT_SCALAR(scraped_data, '$.product_id') as product_id,
    JSON_EXTRACT_SCALAR(scraped_data, '$.url') as product_url,
    JSON_EXTRACT_SCALAR(scraped_data, '$.brand') as brand,
    JSON_EXTRACT_SCALAR(scraped_data, '$.name') as name,
    CAST(JSON_EXTRACT_SCALAR(scraped_data, '$.price') AS FLOAT64) as price,
    COUNT(*) as search_appearances,
    mp_country_code,
    mp_city,
    platform
  FROM `phia-prod-416420.mixpanel.product_result_shown`
  WHERE DATE(time) = '{{ ds }}'  -- Airflow date parameter
  GROUP BY 1,2,3,4,5,6,7,8,9
),

product_clicks AS (
  SELECT
    DATE(time) as date,
    product_id,
    COUNT(*) as clicks,
    AVG(result_index) as avg_position
  FROM `phia-prod-416420.mixpanel.product_result_clicked` 
  WHERE DATE(time) = '{{ ds }}'
  GROUP BY 1,2
),

product_favorites AS (
  SELECT
    DATE(time) as date,
    product_id,
    COUNT(*) as favorites
  FROM `phia-prod-416420.mixpanel.item_favorited`
  WHERE DATE(time) = '{{ ds }}'
  GROUP BY 1,2
)

SELECT 
  ps.date,
  ps.product_id,
  ps.product_url,
  ps.brand,
  ps.name,
  ps.price,
  ps.search_appearances,
  COALESCE(pc.clicks, 0) as clicks,
  COALESCE(pf.favorites, 0) as favorites,
  SAFE_DIVIDE(pc.clicks, ps.search_appearances) as ctr,
  SAFE_DIVIDE(pf.favorites, ps.search_appearances) as favorite_rate,
  
  -- Aggregate user context
  ARRAY_AGG(DISTINCT ps.mp_country_code IGNORE NULLS) as countries,
  ARRAY_AGG(DISTINCT ps.platform IGNORE NULLS) as platforms
  
FROM product_searches ps
LEFT JOIN product_clicks pc ON ps.date = pc.date AND ps.product_id = pc.product_id  
LEFT JOIN product_favorites pf ON ps.date = pf.date AND ps.product_id = pf.product_id
WHERE ps.search_appearances >= 5  -- Filter for meaningful volume
GROUP BY 1,2,3,4,5,6,7,8,9,10,11
ORDER BY search_appearances DESC, clicks DESC
```

**Query 2: Daily Search Trends**
```sql
WITH search_queries AS (
  SELECT
    DATE(time) as date,
    LOWER(TRIM(search_query)) as search_query,
    COUNT(*) as search_count,
    mp_country_code,
    platform
  FROM `phia-prod-416420.mixpanel.text_search`
  WHERE DATE(time) = '{{ ds }}'
    AND LENGTH(search_query) > 2
    AND search_query NOT LIKE '%test%'  -- Filter test queries
  GROUP BY 1,2,3,4
),

search_success AS (
  SELECT 
    DATE(time) as date,
    searchid,
    numproducts,
    JSON_EXTRACT_ARRAY(results) as result_products
  FROM `phia-prod-416420.mixpanel.search_success`
  WHERE DATE(time) = '{{ ds }}'
)

SELECT 
  sq.date,
  sq.search_query,
  SUM(sq.search_count) as total_searches,
  COUNT(DISTINCT sq.mp_country_code) as countries_count,
  ARRAY_AGG(DISTINCT sq.platform IGNORE NULLS) as platforms,
  
  -- Calculate success rate by joining with successful searches
  AVG(CASE WHEN ss.numproducts > 0 THEN 1 ELSE 0 END) as success_rate
  
FROM search_queries sq
LEFT JOIN search_success ss ON sq.date = ss.date
GROUP BY 1,2
HAVING total_searches >= 10  -- Minimum volume threshold
ORDER BY total_searches DESC
```

## Phase 2: Advanced Analytics (Week 3-4)

### 2.1 Trend Velocity Calculation
```sql
-- Calculate trending velocity (growth over past 7 days)
CREATE OR REPLACE VIEW product_trend_velocity AS
WITH daily_metrics AS (
  SELECT 
    date,
    product_id,
    search_appearances + clicks + (favorites * 3) as engagement_score
  FROM daily_product_trends
  WHERE date >= DATE_SUB(CURRENT_DATE(), INTERVAL 14 DAYS)
),

velocity_calc AS (
  SELECT
    product_id,
    AVG(CASE WHEN date >= DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAYS) 
        THEN engagement_score END) as recent_avg,
    AVG(CASE WHEN date < DATE_SUB(CURRENT_DATE(), INTERVAL 7 DAYS) 
        THEN engagement_score END) as previous_avg
  FROM daily_metrics
  GROUP BY product_id
  HAVING recent_avg IS NOT NULL AND previous_avg IS NOT NULL
)

SELECT 
  product_id,
  SAFE_DIVIDE(recent_avg - previous_avg, previous_avg) * 100 as velocity_percentage,
  recent_avg,
  previous_avg
FROM velocity_calc
ORDER BY velocity_percentage DESC
```

### 2.2 Category & Brand Extraction
```sql
-- Extract categories and brands from product data
CREATE OR REPLACE FUNCTION extract_category(product_name STRING, product_url STRING)
RETURNS STRING
LANGUAGE js AS """
  const name = product_name?.toLowerCase() || '';
  const url = product_url?.toLowerCase() || '';
  const text = name + ' ' + url;
  
  // Category keywords mapping
  const categories = {
    'shoes': ['shoe', 'sneaker', 'boot', 'sandal', 'heel', 'flat'],
    'bags': ['bag', 'purse', 'handbag', 'backpack', 'tote', 'clutch'],
    'clothing': ['dress', 'shirt', 'pant', 'jean', 'jacket', 'coat'],
    'jewelry': ['necklace', 'bracelet', 'ring', 'earring', 'jewelry'],
    'accessories': ['watch', 'belt', 'hat', 'scarf', 'sunglasses']
  };
  
  for (let category in categories) {
    if (categories[category].some(keyword => text.includes(keyword))) {
      return category;
    }
  }
  return 'other';
""";
```

## Phase 3: Real-time Dashboard Data (Week 5-6)

### 3.1 Supabase Real-time Views
```sql
-- Current trending products (updated hourly)
CREATE MATERIALIZED VIEW trending_products_now AS
SELECT 
  dp.product_id,
  dp.brand,
  dp.name,
  dp.price_numeric,
  dp.search_appearances,
  dp.clicks,
  dp.favorites,
  dp.click_through_rate,
  tv.velocity_percentage,
  extract_category(dp.name, dp.product_url) as category,
  dp.top_search_queries
FROM daily_product_trends dp
JOIN product_trend_velocity tv ON dp.product_id = tv.product_id
WHERE dp.date = CURRENT_DATE()
ORDER BY tv.velocity_percentage DESC, dp.search_appearances DESC
LIMIT 100;
```

## Phase 4: DAG Implementation Structure

### 4.1 Airflow DAG Architecture
```python
# trending_products_dag.py structure:

default_args = {
    'start_date': datetime(2024, 1, 1),
    'schedule_interval': '@daily',
}

dag = DAG('trending_products_pipeline', default_args=default_args)

# Task 1: Extract core metrics
extract_product_metrics = BigQueryInsertJobOperator(
    task_id='extract_product_metrics',
    configuration={
        "query": {
            "query": PRODUCT_METRICS_QUERY,
            "destinationTable": {
                "projectId": "phia-prod-416420",
                "datasetId": "analytics",
                "tableId": "daily_product_metrics_{{ ds_nodash }}"
            },
            "writeDisposition": "WRITE_TRUNCATE"
        }
    }
)

# Task 2: Extract search trends  
extract_search_trends = BigQueryInsertJobOperator(...)

# Task 3: Calculate trend velocity
calculate_velocity = BigQueryInsertJobOperator(...)

# Task 4: Load to Supabase
load_to_supabase = PythonOperator(
    task_id='load_to_supabase',
    python_callable=load_trending_data_to_supabase
)

# Dependencies
extract_product_metrics >> calculate_velocity >> load_to_supabase
extract_search_trends >> load_to_supabase
```

## Phase 5: Data Quality & Monitoring

### 5.1 Data Quality Checks
- Volume checks (daily row counts)
- Freshness checks (latest data timestamps)  
- Accuracy checks (price ranges, valid URLs)
- Completeness checks (required fields populated)

### 5.2 Key Metrics to Track
- **Daily trending products** (top 100)
- **Search trend keywords** (top 50)
- **Brand performance** (engagement rates)
- **Category trends** (seasonal patterns)
- **Geographic trends** (regional preferences)
- **Platform differences** (iOS vs Web vs Extension)

## Expected Deliverables

1. **Daily**: Top 100 trending products with scores
2. **Weekly**: Brand and category trend reports  
3. **Monthly**: Comprehensive market intelligence report
4. **Real-time**: API endpoints for dashboard integration

## Resource Requirements

- **BigQuery**: ~$50/month for queries
- **Supabase**: Analytics tier for real-time features
- **Airflow**: Existing infrastructure 
- **Development time**: 4-6 weeks for full implementation

This architecture provides scalable, actionable trending insights while maintaining data quality and real-time capabilities.