import csv
import re
from difflib import SequenceMatcher

def normalize_merchant_name_advanced(name):
    """
    Advanced normalization for better merchant matching
    """
    if not name:
        return ""
    
    # Convert to lowercase and remove extra spaces
    name = name.lower().strip()
    
    # Remove quotes
    name = name.replace('"', '').replace("'", "")
    
    # Remove common business suffixes and legal entities
    suffixes = [
        r'\s*(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)\s*$',
        r'\s*,?\s*(a division of [^,]+)\s*$',  # Remove "a division of..." parts
        r'\s*\([^)]*\)\s*',  # Remove parenthetical content
    ]
    
    for suffix in suffixes:
        name = re.sub(suffix, '', name, flags=re.IGNORECASE)
    
    # Remove "the" prefix
    name = re.sub(r'^\s*the\s+', '', name, flags=re.IGNORECASE)
    
    # Remove special characters and normalize spaces
    name = re.sub(r'[^\w\s&]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Remove common geographic indicators
    name = re.sub(r'\s+(us|usa|america|american)\s*$', '', name, flags=re.IGNORECASE)
    
    return name

def is_non_us_merchant(merchant_name):
    """
    Identify merchants that are clearly from non-US countries
    """
    name_lower = merchant_name.lower()
    
    # Clear non-US indicators
    non_us_indicators = [
        'uk', 'united kingdom', 'britain', 'british',
        'canada', 'canadian', 'australia', 'australian',
        'germany', 'german', 'france', 'french',
        'italy', 'italian', 'spain', 'spanish',
        'netherlands', 'dutch', 'belgium', 'belgian',
        'sweden', 'swedish', 'norway', 'norwegian',
        'denmark', 'danish', 'finland', 'finnish',
        'switzerland', 'swiss', 'austria', 'austrian',
        'international limited', 'international ltd',
        'international inc', 'international corp',
        'pty ltd',  # Australian company structure
        '.uk', '.ca', '.au', '.de', '.fr', '.it'
    ]
    
    # Check for exact matches or clear patterns
    for indicator in non_us_indicators:
        if indicator in name_lower:
            return True
    
    # Check for specific patterns
    patterns = [
        r'\b(uk|ca|au|de|fr|it|es|nl|be|se|no|dk|fi|ch|at)\b',  # Country codes
        r'\.uk\b|\.ca\b|\.au\b',  # Domain extensions
        r'\bpty\s+ltd\b',  # Australian company structure
        r'\binternational\s+(limited|ltd|inc|corp)\b'  # International companies
    ]
    
    for pattern in patterns:
        if re.search(pattern, name_lower):
            return True
    
    return False

def similarity_score(a, b):
    """
    Calculate similarity between two strings
    """
    return SequenceMatcher(None, a, b).ratio()

def cleanup_missing_merchants():
    """
    Clean up missing.csv by removing:
    1. Merchants that are actually in current merchants file
    2. Non-US merchants
    """
    print("Cleaning up missing merchants...")
    
    current_file = "download (19).csv"
    missing_file = "missing.csv"
    output_file = "missing_cleaned.csv"
    
    # Read current merchants with advanced normalization
    print("Reading current merchants with advanced matching...")
    current_merchants = set()
    current_merchants_detailed = []
    
    try:
        with open(current_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                # Handle BOM character
                name = row.get("advertiser_name", "") or row.get("\ufeffadvertiser_name", "")
                name = name.strip()
                
                if name:
                    normalized = normalize_merchant_name_advanced(name)
                    current_merchants.add(normalized)
                    current_merchants_detailed.append({
                        'original': name,
                        'normalized': normalized
                    })
    except Exception as e:
        print(f"Error reading current merchants: {e}")
        return
    
    print(f"Found {len(current_merchants)} current merchants")
    
    # Read missing merchants and filter
    print("Reading and filtering missing merchants...")
    cleaned_merchants = []
    removed_existing = 0
    removed_non_us = 0
    similarity_threshold = 0.80  # Slightly lower threshold for better matching
    
    try:
        with open(missing_file, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            for row in reader:
                merchant_name = row.get('merchant_name', '').strip()
                
                if not merchant_name:
                    continue
                
                # Check if it's a non-US merchant
                if is_non_us_merchant(merchant_name):
                    removed_non_us += 1
                    print(f"Removed non-US: {merchant_name}")
                    continue
                
                # Check if it matches any current merchant
                normalized_missing = normalize_merchant_name_advanced(merchant_name)
                
                # Check for exact match first
                if normalized_missing in current_merchants:
                    removed_existing += 1
                    print(f"Removed existing (exact): {merchant_name}")
                    continue
                
                # Check for similar matches
                is_similar = False
                best_match = None
                best_score = 0
                
                for current in current_merchants_detailed:
                    score = similarity_score(normalized_missing, current['normalized'])
                    if score > best_score:
                        best_score = score
                        best_match = current['original']
                
                if best_score >= similarity_threshold:
                    removed_existing += 1
                    print(f"Removed existing (similar {best_score:.2f}): {merchant_name} ≈ {best_match}")
                    continue
                
                # Keep this merchant
                cleaned_merchants.append(row)
    
    except Exception as e:
        print(f"Error reading missing merchants: {e}")
        return
    
    # Write cleaned results
    print(f"Writing {len(cleaned_merchants)} cleaned merchants to {output_file}...")
    
    try:
        with open(output_file, 'w', newline='', encoding='utf-8') as f:
            if cleaned_merchants:
                fieldnames = cleaned_merchants[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(cleaned_merchants)
    except Exception as e:
        print(f"Error writing cleaned file: {e}")
        return
    
    # Print summary
    print(f"\n📊 CLEANUP SUMMARY:")
    print(f"Original missing merchants: {removed_existing + removed_non_us + len(cleaned_merchants):,}")
    print(f"Removed existing merchants: {removed_existing:,}")
    print(f"Removed non-US merchants: {removed_non_us:,}")
    print(f"Final cleaned merchants: {len(cleaned_merchants):,}")
    print(f"Total removed: {removed_existing + removed_non_us:,}")
    
    # Show priority distribution of cleaned merchants
    print(f"\n🎯 Priority distribution of cleaned merchants:")
    priority_counts = {}
    for merchant in cleaned_merchants:
        tier = merchant.get('priority_tier', 'Unknown')
        priority_counts[tier] = priority_counts.get(tier, 0) + 1
    
    for tier, count in sorted(priority_counts.items(), key=lambda x: x[1], reverse=True):
        print(f"  {tier:<35} - {count:4d} merchants")
    
    # Show top cleaned merchants
    print(f"\n🔝 Top 10 cleaned merchants:")
    for i, merchant in enumerate(cleaned_merchants[:10]):
        print(f"  {i+1:2d}. {merchant['merchant_name']:<40} - {merchant['product_count']:>10} products")
    
    print(f"\n✅ Cleanup complete! Check '{output_file}' for the cleaned list.")

if __name__ == "__main__":
    cleanup_missing_merchants()
