table_name,row_count,num_columns,size_mb,description,columns
user_searched_products,1785387,34,1831.64,Search events - user search queries and interactions,"time (TIMESTAMP), search_id (STRING), sampling_factor (FLOAT), results (STRING), product_search_options (STRING), product_search_input (STRING), product_search_filters (STRING), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING)..."
product_result_shown,1347572,62,773.81,"Product interaction events - product views, clicks, favorites","version (STRING), time (TIMESTAMP), source (STRING), search_id (STRING), scraped_data (STRING), sampling_factor (FLOAT), product_url (STRING), product_id (STRING), origin (STRING), mp_user_id (STRING)..."
text_search,312920,48,145.63,Search events - user search queries and interactions,"time (TIMESTAMP), source (STRING), search_query (STRING), sampling_factor (FLOAT), platform (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT), mp_region (STRING)..."
search_success,76219,46,586.2,Search events - user search queries and interactions,"time (TIMESTAMP), searchid (STRING), sampling_factor (FLOAT), results (STRING), numproducts (FLOAT), mp_user_id (STRING), mp_sent_by_lib_version (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT), mp_region (STRING)..."
top_results_clicked,60002,33,38.52,User click events - tracks when users click on top results,"url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), price (STRING), phia_id (STRING), name (STRING), mp_user_id (STRING), mp_region (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING)..."
view_similar_items_clicked,50861,28,28.98,User click events - tracks when users click on view similar items,"url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), price (FLOAT), phia_id (STRING), name (STRING), mp_user_id (STRING), mp_region (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING)..."
product_result_clicked,50040,64,31.17,User click events - tracks when users click on product result,"time (TIMESTAMP), sampling_factor (FLOAT), result_index (FLOAT), product_id (STRING), pathname (STRING), mp_user_id (STRING), mp_source (STRING), mp_processing_time_ms (FLOAT), mp_os (STRING), mp_mp_api_timestamp_ms (FLOAT)..."
explore_product_opened,49240,48,23.34,"Product interaction events - product views, clicks, favorites","user_profile_id (STRING), user_id (STRING), type (STRING), time (TIMESTAMP), sampling_factor (FLOAT), platform (STRING), phia_id (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT)..."
landing_page_popular_search_item_clicked,19269,46,8.59,User click events - tracks when users click on landing page popular search item,"time (TIMESTAMP), search_query (STRING), sampling_factor (FLOAT), platform (STRING), phia_id (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT), mp_region (STRING)..."
item_favorited,12276,48,7.05,Favorites/wishlist events - item saving and management,"username (STRING), user_profile_id (STRING), user_id (STRING), time (TIMESTAMP), status (STRING), source (STRING), sampling_factor (FLOAT), productid (STRING), platform (STRING), phone_number (STRING)..."
collection_product_card_clicked,6716,27,4.1,User click events - tracks when users click on collection product card,"url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), productname (STRING), productid (STRING), phia_id (STRING), pathname (STRING), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING)..."
landing_page_showcased_product_clicked,5394,24,1.61,User click events - tracks when users click on landing page showcased product,"url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), pathname (STRING), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING), mp_platform (STRING), mp_mp_api_timestamp_ms (FLOAT), mp_mp_api_endpoint (STRING)..."
product_page_viewed,3903,22,1.2,Page/content view events - tracks when users view product page,"url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), product_id (STRING), pathname (STRING), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING), mp_platform (STRING), mp_os (STRING)..."
product_card_clicked,3062,37,1.66,User click events - tracks when users click on product card,"time (TIMESTAMP), sampling_factor (FLOAT), productid (STRING), mp_user_id (STRING), mp_sent_by_lib_version (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT), mp_region (STRING), mp_referring_domain (STRING), mp_referrer (STRING)..."
product_favorited,2481,24,1.37,"Product interaction events - product views, clicks, favorites","url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), product_id (STRING), phia_id (STRING), mp_user_id (STRING), mp_region (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING), mp_platform (STRING)..."
user_save_added,2187,19,0.45,User interaction events related to user save added,"time (TIMESTAMP), sampling_factor (FLOAT), origin (FLOAT), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_mp_api_timestamp_ms (FLOAT), mp_mp_api_endpoint (STRING), mp_lib_version (STRING), mp_lib (STRING), mp_insert_id (STRING)..."
landing_page_trending_items_clicked,1023,37,0.49,User click events - tracks when users click on landing page trending items,"time (TIMESTAMP), sampling_factor (FLOAT), product_name (STRING), product_id (STRING), platform (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT), mp_region (STRING)..."
explore_product_favorited,711,51,0.49,"Product interaction events - product views, clicks, favorites","user_profile_id (STRING), user_id (STRING), time (TIMESTAMP), sampling_factor (FLOAT), producturl (STRING), productname (STRING), productid (STRING), platform (STRING), phia_id (STRING), mp_wifi (BOOLEAN)..."
product_result_shared,330,35,0.19,"Product interaction events - product views, clicks, favorites","time (TIMESTAMP), search_id (STRING), sampling_factor (FLOAT), product_id (STRING), pathname (STRING), mp_user_id (STRING), mp_source (STRING), mp_processing_time_ms (FLOAT), mp_os (STRING), mp_mp_api_timestamp_ms (FLOAT)..."
collection_shared,295,47,0.14,Collection/wishlist events - user collection management,"user_profile_id (STRING), user_id (STRING), time (TIMESTAMP), sampling_factor (FLOAT), platform (STRING), phia_id (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT)..."
product_saved,247,52,0.23,"Product interaction events - product views, clicks, favorites","utm_source (STRING), utm_content (STRING), utm_campaign (STRING), time (TIMESTAMP), sampling_factor (FLOAT), product_url (STRING), product_title (STRING), product_price (FLOAT), product_image (STRING), product_brand (STRING)..."
product_clicked,216,38,0.13,User click events - tracks when users click on product,"time (TIMESTAMP), sampling_factor (FLOAT), product_url (STRING), product_name (STRING), product_id (STRING), platform (STRING), phia_id (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT)..."
collection_card_clicked,180,23,0.08,User click events - tracks when users click on collection card,"url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), phia_id (STRING), pathname (STRING), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING), mp_platform (STRING), mp_os (STRING)..."
product_share_visited,38,20,0.01,"Product interaction events - product views, clicks, favorites","url (STRING), time (TIMESTAMP), sampling_factor (FLOAT), product_share_id (STRING), product_id (STRING), pathname (STRING), mp_user_id (STRING), mp_processing_time_ms (FLOAT), mp_platform_version (STRING), mp_platform (STRING)..."
brand_search,4,35,0.0,Search events - user search queries and interactions,"time (TIMESTAMP), source (STRING), search_query (STRING), sampling_factor (FLOAT), platform (STRING), phia_id (STRING), mp_wifi (BOOLEAN), mp_user_id (STRING), mp_screen_width (FLOAT), mp_screen_height (FLOAT)..."
product_page_visited,1,20,0.0,"Product interaction events - product views, clicks, favorites","version (STRING), time (TIMESTAMP), source (STRING), scraped_data (STRING), sampling_factor (FLOAT), product_url (STRING), product_id (STRING), origin (STRING), mp_user_id (STRING), mp_source (STRING)..."
