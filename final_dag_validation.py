#!/usr/bin/env python3
"""
Final DAG Validation - Simulate the exact production DAG flow and output complete Supabase payload
"""

from google.cloud import bigquery
import pandas as pd
import json
import logging
from datetime import datetime, timezone

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

PROJECT_ID = 'phia-prod-416420'
MIXPANEL_DATASET = 'mixpanel'
TEST_DATE = '2025-07-21'

def execute_trending_products_query():
    """Execute the exact trending products query from the production DAG"""
    client = bigquery.Client(project=PROJECT_ID)
    
    # Exact query from the DAG with template variables replaced
    query = """
    WITH product_searches AS (
      SELECT
        time,
        DATE(time) as search_date,
        JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName') as product_name,
        JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedBrand') as brand,
        CAST(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedPrice') AS FLOAT64) as price,
        JSON_EXTRACT_SCALAR(product_search_input, '$.productUrl') as product_url,
        search_type,
        phia_id,
        distinct_id,
        platform
      FROM `phia-prod-416420.mixpanel.user_searched_products`
      WHERE DATE(time) BETWEEN DATE_SUB(DATE('2025-07-21'), INTERVAL 7 DAY) 
        AND DATE('2025-07-21')
        AND product_search_input IS NOT NULL
        AND JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName') IS NOT NULL
        AND LENGTH(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName')) > 3
    ),
    product_metrics AS (
      SELECT
        LOWER(TRIM(product_name)) as product_key,
        ANY_VALUE(product_name) as product_name,
        ANY_VALUE(brand) as brand,
        ANY_VALUE(product_url) as sample_url,
        COUNT(*) as search_count,
        COUNT(DISTINCT phia_id) as unique_users,
        COUNT(DISTINCT distinct_id) as unique_sessions,
        COUNT(DISTINCT search_date) as days_searched,
        AVG(price) as avg_price,
        MIN(time) as first_searched,
        MAX(time) as last_searched,
        SUM(CASE WHEN search_date >= DATE_SUB(DATE('2025-07-21'), INTERVAL 3 DAY) 
            THEN 1 ELSE 0 END) as recent_searches,
        SUM(CASE WHEN search_date < DATE_SUB(DATE('2025-07-21'), INTERVAL 3 DAY) 
            THEN 1 ELSE 0 END) as previous_searches,
        COUNTIF(platform = 'IOS_APP') as ios_searches,
        COUNTIF(platform = 'IOS_SAFARI_EXTENSION') as safari_searches,
        COUNTIF(search_type = 'MULTI_MODAL') as image_searches,
        COUNTIF(search_type = 'TEXT_SEARCH') as text_searches
      FROM product_searches
      WHERE product_name NOT LIKE '%test%'
        AND product_name NOT LIKE '%undefined%'
        AND brand IS NOT NULL
        AND LENGTH(brand) > 1
      GROUP BY product_key
      HAVING search_count >= 2
    )
    SELECT
      product_key,
      product_name,
      brand,
      sample_url,
      search_count,
      unique_users,
      unique_sessions,
      days_searched,
      ROUND(avg_price, 2) as avg_price,
      first_searched,
      last_searched,
      ROUND(SAFE_DIVIDE(recent_searches - previous_searches, 
                        NULLIF(previous_searches, 0)) * 100, 2) as trending_score,
      recent_searches,
      previous_searches,
      ROUND(SAFE_DIVIDE(ios_searches, search_count) * 100, 2) as ios_percentage,
      ROUND(SAFE_DIVIDE(safari_searches, search_count) * 100, 2) as safari_percentage,
      ROUND(SAFE_DIVIDE(image_searches, search_count) * 100, 2) as image_search_percentage,
      ROUND(SAFE_DIVIDE(search_count, unique_users), 2) as searches_per_user,
      ROUND(SAFE_DIVIDE(search_count, days_searched), 2) as searches_per_day
    FROM product_metrics
    ORDER BY search_count DESC, trending_score DESC
    LIMIT 50
    """
    
    try:
        df = client.query(query).to_dataframe()
        logger.info(f"✅ Trending Products Task: {len(df)} records")
        return df
    except Exception as e:
        logger.error(f"❌ Trending Products Task failed: {e}")
        return None

def execute_search_trends_query():
    """Execute the exact search trends query from the production DAG"""
    client = bigquery.Client(project=PROJECT_ID)
    
    # Exact query from the DAG with template variables replaced
    query = """
    WITH search_data AS (
      SELECT
        LOWER(TRIM(search_query)) as query,
        search_query as original_query,
        DATE(time) as search_date,
        time as search_timestamp,
        platform,
        mp_os,
        phia_id,
        user_id,
        distinct_id,
        mp_user_id
      FROM `phia-prod-416420.mixpanel.text_search`
      WHERE DATE(time) BETWEEN DATE_SUB(DATE('2025-07-21'), INTERVAL 7 DAY) 
        AND DATE('2025-07-21')
        AND LENGTH(search_query) > 2
        AND search_query NOT LIKE '%test%'
        AND search_query NOT LIKE '%undefined%'
    ),
    query_metrics AS (
      SELECT
        query,
        ANY_VALUE(original_query) as example_original_query,
        COUNT(*) as search_count,
        COUNT(DISTINCT search_date) as days_searched,
        COUNT(DISTINCT phia_id) as unique_users,
        COUNT(DISTINCT distinct_id) as unique_sessions,
        MIN(search_timestamp) as first_search,
        MAX(search_timestamp) as last_search,
        SUM(CASE WHEN search_date >= DATE_SUB(DATE('2025-07-21'), INTERVAL 3 DAY) 
            THEN 1 ELSE 0 END) as recent_searches,
        SUM(CASE WHEN search_date < DATE_SUB(DATE('2025-07-21'), INTERVAL 3 DAY) 
            THEN 1 ELSE 0 END) as previous_searches,
        COUNTIF(platform = 'iOS') as ios_searches,
        COUNTIF(platform = 'Web') as web_searches,
        COUNTIF(platform = 'android') as android_searches,
        STRING_AGG(DISTINCT mp_os, ', ' LIMIT 5) as operating_systems
      FROM search_data
      GROUP BY query
      HAVING search_count >= 5
    )
    SELECT
      query,
      example_original_query,
      search_count,
      days_searched,
      unique_users,
      unique_sessions,
      first_search,
      last_search,
      ROUND(SAFE_DIVIDE(recent_searches - previous_searches, 
                        NULLIF(previous_searches, 0)) * 100, 2) as growth_percentage,
      recent_searches,
      previous_searches,
      ROUND(SAFE_DIVIDE(ios_searches, search_count) * 100, 2) as ios_percentage,
      ROUND(SAFE_DIVIDE(web_searches, search_count) * 100, 2) as web_percentage,
      ROUND(SAFE_DIVIDE(android_searches, search_count) * 100, 2) as android_percentage,
      operating_systems,
      ROUND(SAFE_DIVIDE(search_count, days_searched), 2) as avg_searches_per_day,
      ROUND(SAFE_DIVIDE(search_count, unique_users), 2) as searches_per_user
    FROM query_metrics
    ORDER BY search_count DESC
    LIMIT 50
    """
    
    try:
        df = client.query(query).to_dataframe()
        logger.info(f"✅ Search Trends Task: {len(df)} records")
        return df
    except Exception as e:
        logger.error(f"❌ Search Trends Task failed: {e}")
        return None

def execute_style_showdowns_query():
    """Execute the exact style showdowns query from the production DAG"""
    client = bigquery.Client(project=PROJECT_ID)
    
    # Exact query from the DAG with template variables replaced
    query = """
    WITH product_attributes AS (
      SELECT 
        JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName') as product_name,
        JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedBrand') as brand,
        CAST(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedPrice') AS FLOAT64) as price,
        COUNT(*) as search_volume,
        COUNT(DISTINCT phia_id) as unique_users,
        LOWER(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName')) as product_name_lower
      FROM `phia-prod-416420.mixpanel.user_searched_products`
      WHERE DATE(time) BETWEEN DATE_SUB(DATE('2025-07-21'), INTERVAL 7 DAY) 
        AND DATE('2025-07-21')
        AND JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName') IS NOT NULL
        AND LENGTH(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName')) > 3
      GROUP BY product_name, brand, price, product_name_lower
      HAVING search_volume >= 2
    ),
    color_analysis AS (
      SELECT 
        CASE 
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bblack\\b') THEN 'black'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bwhite\\b') THEN 'white'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bred\\b|\\bcrimson\\b|\\bcherry\\b') THEN 'red'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bblue\\b|\\bnavy\\b|\\bcobalt\\b') THEN 'blue'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bgreen\\b|\\bolive\\b|\\bemerald\\b') THEN 'green'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bpink\\b|\\brose\\b|\\bblush\\b') THEN 'pink'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bgold\\b|\\bgolden\\b') THEN 'gold'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bsilver\\b|\\bmetallic\\b') THEN 'silver'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bbrown\\b|\\btan\\b|\\bcamel\\b|\\bcognac\\b') THEN 'brown'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bbeige\\b|\\bcream\\b|\\bivory\\b') THEN 'beige'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bgray\\b|\\bgrey\\b|\\bcharcoal\\b') THEN 'gray'
        END as color_attribute,
        SUM(search_volume) as total_searches
      FROM product_attributes
      WHERE REGEXP_CONTAINS(product_name_lower, r'\\b(black|white|red|blue|green|pink|gold|silver|brown|beige|gray|grey|crimson|cherry|navy|cobalt|olive|emerald|rose|blush|golden|metallic|tan|camel|cognac|cream|ivory|charcoal)\\b')
      GROUP BY color_attribute
    ),
    material_analysis AS (
      SELECT 
        CASE 
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bcotton\\b') THEN 'cotton'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\blinen\\b') THEN 'linen'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bsilk\\b') THEN 'silk'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bwool\\b|\\bcashmere\\b') THEN 'wool'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bleather\\b') THEN 'leather'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bdenim\\b') THEN 'denim'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\blace\\b') THEN 'lace'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bmesh\\b') THEN 'mesh'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bsatin\\b') THEN 'satin'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bvelvet\\b') THEN 'velvet'
        END as material_attribute,
        SUM(search_volume) as total_searches
      FROM product_attributes
      WHERE REGEXP_CONTAINS(product_name_lower, r'\\b(cotton|linen|silk|wool|cashmere|leather|denim|lace|mesh|satin|velvet)\\b')
      GROUP BY material_attribute
    ),
    style_analysis AS (
      SELECT 
        CASE 
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bcropped\\b|\\bcrop\\b') THEN 'cropped'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bpleated\\b') THEN 'pleated'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bfitted\\b|\\bbodycon\\b') THEN 'fitted'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\boversized\\b|\\bloose\\b') THEN 'oversized'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bmini\\b') THEN 'mini'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bmidi\\b') THEN 'midi'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bmaxi\\b') THEN 'maxi'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\bsleeveless\\b|\\btank\\b') THEN 'sleeveless'
          WHEN REGEXP_CONTAINS(product_name_lower, r'\\blong sleeve\\b|\\blong-sleeve\\b') THEN 'long_sleeve'
        END as style_attribute,
        SUM(search_volume) as total_searches
      FROM product_attributes
      WHERE REGEXP_CONTAINS(product_name_lower, r'\\b(cropped|crop|pleated|fitted|bodycon|oversized|loose|mini|midi|maxi|sleeveless|tank|long sleeve|long-sleeve)\\b')
      GROUP BY style_attribute
    ),
    showdown_comparisons AS (
      -- Color comparisons
      SELECT 
        'color' as category,
        'Black vs White' as comparison_type,
        CONCAT(
          'Black is ', 
          ROUND(((black.total_searches - white.total_searches) / white.total_searches) * 100, 1),
          '% more popular than White'
        ) as comparison,
        CONCAT('(', black.total_searches, ' vs. ', white.total_searches, ' searches)') as metrics,
        'Monochrome is still queen — but black is reigning supreme.' as insight,
        black.total_searches as primary_value,
        white.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM color_analysis WHERE color_attribute = 'black') black
      CROSS JOIN 
        (SELECT total_searches FROM color_analysis WHERE color_attribute = 'white') white
      WHERE black.total_searches > 0 AND white.total_searches > 0
      
      UNION ALL
      
      SELECT 
        'color' as category,
        'Gold vs Silver' as comparison_type,
        CONCAT(
          'Gold is ', 
          ROUND(((gold.total_searches - silver.total_searches) / silver.total_searches) * 100, 1),
          '% more popular than Silver'
        ) as comparison,
        CONCAT('(', gold.total_searches, ' vs. ', silver.total_searches, ' searches)') as metrics,
        'It\\'s not even close. Gold is giving goddess.' as insight,
        gold.total_searches as primary_value,
        silver.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM color_analysis WHERE color_attribute = 'gold') gold
      CROSS JOIN 
        (SELECT total_searches FROM color_analysis WHERE color_attribute = 'silver') silver
      WHERE gold.total_searches > 0 AND silver.total_searches > 0
      
      UNION ALL
      
      -- Material comparisons
      SELECT 
        'material' as category,
        'Cotton vs Linen' as comparison_type,
        CONCAT(
          'Cotton is ', 
          ROUND(((cotton.total_searches - linen.total_searches) / linen.total_searches) * 100, 1),
          '% more popular than Linen'
        ) as comparison,
        CONCAT('(', cotton.total_searches, ' vs. ', linen.total_searches, ' searches)') as metrics,
        'Easy, breezy, cotton-core energy is winning.' as insight,
        cotton.total_searches as primary_value,
        linen.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM material_analysis WHERE material_attribute = 'cotton') cotton
      CROSS JOIN 
        (SELECT total_searches FROM material_analysis WHERE material_attribute = 'linen') linen
      WHERE cotton.total_searches > 0 AND linen.total_searches > 0
      
      UNION ALL
      
      SELECT 
        'material' as category,
        'Silk vs Cotton' as comparison_type,
        CONCAT(
          'Silk is ', 
          ROUND(((silk.total_searches - cotton.total_searches) / cotton.total_searches) * 100, 1),
          '% more popular than Cotton'
        ) as comparison,
        CONCAT('(', silk.total_searches, ' vs. ', cotton.total_searches, ' searches)') as metrics,
        'Luxury fabrics are having a moment — silk is sliding into the spotlight.' as insight,
        silk.total_searches as primary_value,
        cotton.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM material_analysis WHERE material_attribute = 'silk') silk
      CROSS JOIN 
        (SELECT total_searches FROM material_analysis WHERE material_attribute = 'cotton') cotton
      WHERE silk.total_searches > 0 AND cotton.total_searches > 0
      
      UNION ALL
      
      SELECT 
        'material' as category,
        'Lace vs Mesh' as comparison_type,
        CONCAT(
          'Lace is ', 
          ROUND(((lace.total_searches - mesh.total_searches) / mesh.total_searches) * 100, 1),
          '% more popular than Mesh'
        ) as comparison,
        CONCAT('(', lace.total_searches, ' vs. ', mesh.total_searches, ' searches)') as metrics,
        'Soft & romantic > sporty sheer? You decide.' as insight,
        lace.total_searches as primary_value,
        mesh.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM material_analysis WHERE material_attribute = 'lace') lace
      CROSS JOIN 
        (SELECT total_searches FROM material_analysis WHERE material_attribute = 'mesh') mesh
      WHERE lace.total_searches > 0 AND mesh.total_searches > 0
      
      UNION ALL
      
      -- Style comparisons
      SELECT 
        'style' as category,
        'Mini vs Midi' as comparison_type,
        CONCAT(
          'Mini is ', 
          ROUND(((mini.total_searches - midi.total_searches) / midi.total_searches) * 100, 1),
          '% more popular than Midi'
        ) as comparison,
        CONCAT('(', mini.total_searches, ' vs. ', midi.total_searches, ' searches)') as metrics,
        'Short & sweet is beating out midi magic — by a lot.' as insight,
        mini.total_searches as primary_value,
        midi.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM style_analysis WHERE style_attribute = 'mini') mini
      CROSS JOIN 
        (SELECT total_searches FROM style_analysis WHERE style_attribute = 'midi') midi
      WHERE mini.total_searches > 0 AND midi.total_searches > 0
      
      UNION ALL
      
      SELECT 
        'style' as category,
        'Cropped vs Pleated' as comparison_type,
        CONCAT(
          CASE 
            WHEN cropped.total_searches > pleated.total_searches THEN 'Cropped is '
            ELSE 'Pleated is '
          END,
          ROUND(ABS((cropped.total_searches - pleated.total_searches) / 
                    GREATEST(cropped.total_searches, pleated.total_searches)) * 100, 1),
          '% more popular than ',
          CASE 
            WHEN cropped.total_searches > pleated.total_searches THEN 'Pleated'
            ELSE 'Cropped'
          END
        ) as comparison,
        CONCAT('(', cropped.total_searches, ' vs. ', pleated.total_searches, ' searches)') as metrics,
        CASE 
          WHEN cropped.total_searches > pleated.total_searches 
          THEN 'Midriff reveals are edging out preppy skirts — just barely.'
          ELSE 'Preppy pleats are beating out crop tops — classic wins.'
        END as insight,
        cropped.total_searches as primary_value,
        pleated.total_searches as secondary_value
      FROM 
        (SELECT total_searches FROM style_analysis WHERE style_attribute = 'cropped') cropped
      CROSS JOIN 
        (SELECT total_searches FROM style_analysis WHERE style_attribute = 'pleated') pleated
      WHERE cropped.total_searches > 0 AND pleated.total_searches > 0
    )
    SELECT 
      category,
      comparison_type,
      comparison,
      metrics,
      insight,
      primary_value,
      secondary_value,
      '2025-07-21' as analysis_date
    FROM showdown_comparisons
    ORDER BY category, primary_value DESC
    """
    
    try:
        df = client.query(query).to_dataframe()
        logger.info(f"✅ Style Showdowns Task: {len(df)} records")
        return df
    except Exception as e:
        logger.error(f"❌ Style Showdowns Task failed: {e}")
        return None

def execute_brand_trends_query():
    """Execute the exact brand trends query from the production DAG"""
    client = bigquery.Client(project=PROJECT_ID)
    
    # Exact query from the DAG with template variables replaced
    query = """
    WITH brand_searches AS (
      -- Brand data from search queries
      SELECT
        LOWER(TRIM(search_query)) as search_term,
        CASE 
          WHEN LOWER(search_query) LIKE '%louis vuitton%' THEN 'louis vuitton'
          WHEN LOWER(search_query) LIKE '%chanel%' THEN 'chanel'
          WHEN LOWER(search_query) LIKE '%gucci%' THEN 'gucci'
          WHEN LOWER(search_query) LIKE '%prada%' THEN 'prada'
          WHEN LOWER(search_query) LIKE '%hermes%' THEN 'hermes'
          WHEN LOWER(search_query) LIKE '%dior%' THEN 'dior'
          WHEN LOWER(search_query) LIKE '%coach%' THEN 'coach'
          WHEN LOWER(search_query) LIKE '%tiffany%' THEN 'tiffany & co'
          WHEN LOWER(search_query) LIKE '%cartier%' THEN 'cartier'
          WHEN LOWER(search_query) LIKE '%bottega%' THEN 'bottega veneta'
          WHEN LOWER(search_query) LIKE '%celine%' THEN 'celine'
          WHEN LOWER(search_query) LIKE '%loewe%' THEN 'loewe'
          WHEN LOWER(search_query) LIKE '%goyard%' THEN 'goyard'
          WHEN LOWER(search_query) LIKE '%golden goose%' THEN 'golden goose'
          WHEN LOWER(search_query) LIKE '%david yurman%' THEN 'david yurman'
          WHEN LOWER(search_query) LIKE '%van cleef%' THEN 'van cleef & arpels'
          WHEN LOWER(search_query) LIKE '%miu miu%' THEN 'miu miu'
          WHEN LOWER(search_query) LIKE '%ysl%' OR LOWER(search_query) LIKE '%saint laurent%' THEN 'saint laurent'
          WHEN LOWER(search_query) LIKE '%lululemon%' THEN 'lululemon'
          WHEN LOWER(search_query) LIKE '%alo%' THEN 'alo yoga'
          ELSE NULL
        END as brand,
        DATE(time) as event_date,
        time as event_timestamp,
        platform,
        phia_id,
        distinct_id,
        'search' as event_type,
        1 as engagement_weight
      FROM `phia-prod-416420.mixpanel.text_search`
      WHERE DATE(time) BETWEEN DATE_SUB(DATE('2025-07-21'), INTERVAL 7 DAY) 
        AND DATE('2025-07-21')
        AND LENGTH(search_query) > 2
    ),
    brand_product_searches AS (
      -- Brand data from product searches
      SELECT
        JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedName') as product_name,
        LOWER(TRIM(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedBrand'))) as brand,
        DATE(time) as event_date,
        time as event_timestamp,
        platform,
        phia_id,
        distinct_id,
        'product_search' as event_type,
        2 as engagement_weight
      FROM `phia-prod-416420.mixpanel.user_searched_products`
      WHERE DATE(time) BETWEEN DATE_SUB(DATE('2025-07-21'), INTERVAL 7 DAY) 
        AND DATE('2025-07-21')
        AND JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedBrand') IS NOT NULL
        AND LENGTH(JSON_EXTRACT_SCALAR(product_search_input, '$.scrapedBrand')) > 1
    ),
    combined_brand_engagement AS (
      SELECT brand, event_date, event_timestamp, platform, phia_id, distinct_id, event_type, engagement_weight
      FROM brand_searches 
      WHERE brand IS NOT NULL
      
      UNION ALL
      
      SELECT brand, event_date, event_timestamp, platform, phia_id, distinct_id, event_type, engagement_weight
      FROM brand_product_searches
      WHERE brand IS NOT NULL
        AND brand NOT IN ('unknown', 'null', '', 'none', 'undefined')
        AND brand NOT LIKE '%test%'
    ),
    brand_metrics AS (
      SELECT
        brand,
        SUM(engagement_weight) as total_engagement,
        COUNT(*) as total_interactions,
        COUNT(DISTINCT phia_id) as unique_users,
        COUNT(DISTINCT distinct_id) as unique_sessions,
        COUNT(DISTINCT event_date) as days_active,
        SUM(CASE WHEN event_date >= DATE_SUB(DATE('2025-07-21'), INTERVAL 3 DAY) 
            THEN engagement_weight ELSE 0 END) as recent_engagement,
        SUM(CASE WHEN event_date < DATE_SUB(DATE('2025-07-21'), INTERVAL 3 DAY) 
            THEN engagement_weight ELSE 0 END) as previous_engagement,
        COUNTIF(event_type = 'search') as search_mentions,
        COUNTIF(event_type = 'product_search') as product_searches,
        COUNTIF(platform = 'iOS' OR platform = 'IOS_APP') as ios_engagement,
        COUNTIF(platform = 'IOS_SAFARI_EXTENSION') as safari_engagement,
        MIN(event_timestamp) as first_seen,
        MAX(event_timestamp) as last_seen
      FROM combined_brand_engagement
      GROUP BY brand
      HAVING total_engagement >= 3
    )
    SELECT
      brand,
      total_engagement,
      total_interactions,
      unique_users,
      unique_sessions,
      days_active,
      search_mentions,
      product_searches,
      ROUND(SAFE_DIVIDE(recent_engagement - previous_engagement, 
                        NULLIF(previous_engagement, 0)) * 100, 2) as trend_velocity,
      recent_engagement,
      previous_engagement,
      ROUND(SAFE_DIVIDE(ios_engagement, total_engagement) * 100, 2) as ios_percentage,
      ROUND(SAFE_DIVIDE(safari_engagement, total_engagement) * 100, 2) as safari_percentage,
      first_seen,
      last_seen,
      ROUND(SAFE_DIVIDE(total_engagement, unique_users), 2) as engagement_per_user,
      ROUND(SAFE_DIVIDE(total_engagement, days_active), 2) as engagement_per_day,
      ROUND(SAFE_DIVIDE(search_mentions, total_interactions) * 100, 2) as search_percentage,
      ROUND(SAFE_DIVIDE(product_searches, total_interactions) * 100, 2) as product_search_percentage
    FROM brand_metrics
    ORDER BY total_engagement DESC
    LIMIT 50
    """
    
    try:
        df = client.query(query).to_dataframe()
        logger.info(f"✅ Brand Trends Task: {len(df)} records")
        return df
    except Exception as e:
        logger.error(f"❌ Brand Trends Task failed: {e}")
        return None

def convert_dataframe_to_records(df):
    """Convert DataFrame to JSON-serializable records (mimics DAG behavior)"""
    if df is None or len(df) == 0:
        return []
    
    records = []
    for _, row in df.iterrows():
        record = {}
        for col, value in row.items():
            if pd.isna(value):
                record[col] = None
            elif isinstance(value, (pd.Timestamp,)):
                record[col] = value.isoformat()
            elif isinstance(value, (pd.Int64Dtype, int)):
                record[col] = int(value) if not pd.isna(value) else None
            elif isinstance(value, (pd.Float64Dtype, float)):
                record[col] = float(value) if not pd.isna(value) else None
            else:
                record[col] = str(value) if value is not None else None
        records.append(record)
    return records

def create_supabase_payload(trending_products, search_trends, brand_trends, style_showdowns, execution_date):
    """Create the exact Supabase payload that the DAG would push"""
    
    # Prepare consolidated data (exact structure from DAG)
    insights_data = {
        'trending_products': trending_products,
        'search_trends': search_trends,
        'brand_performance': brand_trends,
        'style_showdowns': style_showdowns,
        'metadata': {
            'analysis_date': execution_date,
            'lookback_days': 7,
            'total_products': len(trending_products) if trending_products else 0,
            'total_searches': len(search_trends) if search_trends else 0,
            'total_brands': len(brand_trends) if brand_trends else 0,
            'total_showdowns': len(style_showdowns) if style_showdowns else 0,
            'generated_at': datetime.utcnow().isoformat()
        }
    }
    
    # This is the exact payload that would be sent to Supabase
    supabase_payload = {
        'insight_date': execution_date,
        'insight_type': 'daily_trending',
        'data': insights_data
    }
    
    return supabase_payload

def analyze_payload(payload):
    """Analyze the payload for quality and completeness"""
    
    analysis = {
        'payload_summary': {
            'insight_date': payload['insight_date'],
            'insight_type': payload['insight_type'],
            'total_size_bytes': len(json.dumps(payload, default=str)),
            'total_size_kb': round(len(json.dumps(payload, default=str)) / 1024, 2),
        },
        'data_breakdown': {
            'trending_products': len(payload['data']['trending_products']),
            'search_trends': len(payload['data']['search_trends']),
            'brand_performance': len(payload['data']['brand_performance']),
        },
        'metadata': payload['data']['metadata'],
        'sample_data': {
            'top_product': payload['data']['trending_products'][0] if payload['data']['trending_products'] else None,
            'top_search': payload['data']['search_trends'][0] if payload['data']['search_trends'] else None,
            'top_brand': payload['data']['brand_performance'][0] if payload['data']['brand_performance'] else None,
        }
    }
    
    return analysis

def main():
    logger.info("="*80)
    logger.info("FINAL DAG VALIDATION - COMPLETE PRODUCTION SIMULATION")
    logger.info("="*80)
    
    execution_date = TEST_DATE
    
    # Step 1: Execute all three BigQuery tasks (parallel in real DAG)
    logger.info("Step 1: Executing BigQuery tasks...")
    logger.info("  - Running trending products query...")
    trending_products_df = execute_trending_products_query()
    
    logger.info("  - Running search trends query...")
    search_trends_df = execute_search_trends_query()
    
    logger.info("  - Running brand trends query...")
    brand_trends_df = execute_brand_trends_query()
    
    logger.info("  - Running style showdowns query...")
    style_showdowns_df = execute_style_showdowns_query()
    
    # Step 2: Convert to records (mimics fetch_query_results function)
    logger.info("\nStep 2: Converting query results to records...")
    trending_products = convert_dataframe_to_records(trending_products_df)
    search_trends = convert_dataframe_to_records(search_trends_df)
    brand_trends = convert_dataframe_to_records(brand_trends_df)
    style_showdowns = convert_dataframe_to_records(style_showdowns_df)
    
    logger.info(f"  ✅ Trending Products: {len(trending_products)} records")
    logger.info(f"  ✅ Search Trends: {len(search_trends)} records")
    logger.info(f"  ✅ Brand Performance: {len(brand_trends)} records")
    logger.info(f"  ✅ Style Showdowns: {len(style_showdowns)} records")
    
    # Step 3: Create Supabase payload (mimics push_all_to_supabase function)
    logger.info("\nStep 3: Creating Supabase payload...")
    supabase_payload = create_supabase_payload(trending_products, search_trends, brand_trends, execution_date)
    
    # Step 4: Analyze payload
    logger.info("\nStep 4: Analyzing payload...")
    analysis = analyze_payload(supabase_payload)
    
    # Save outputs
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Complete Supabase payload (exactly what would be sent)
    payload_file = f"final_supabase_payload_{timestamp}.json"
    with open(payload_file, 'w') as f:
        json.dump(supabase_payload, f, indent=2, default=str)
    
    # Analysis report
    analysis_file = f"final_payload_analysis_{timestamp}.json"
    with open(analysis_file, 'w') as f:
        json.dump(analysis, f, indent=2, default=str)
    
    # Raw data files for inspection
    if trending_products_df is not None:
        trending_csv = f"final_trending_products_{timestamp}.csv"
        trending_products_df.to_csv(trending_csv, index=False)
        
    if search_trends_df is not None:
        search_csv = f"final_search_trends_{timestamp}.csv"
        search_trends_df.to_csv(search_csv, index=False)
        
    if brand_trends_df is not None:
        brand_csv = f"final_brand_trends_{timestamp}.csv"
        brand_trends_df.to_csv(brand_csv, index=False)
    
    # Final results
    logger.info("\n" + "="*80)
    logger.info("FINAL DAG VALIDATION RESULTS")
    logger.info("="*80)
    
    logger.info(f"📄 SUPABASE PAYLOAD: {payload_file}")
    logger.info(f"📊 ANALYSIS REPORT: {analysis_file}")
    logger.info(f"📈 RAW DATA FILES: final_*_{timestamp}.csv")
    
    logger.info(f"\n📦 PAYLOAD SUMMARY:")
    logger.info(f"   Size: {analysis['payload_summary']['total_size_kb']} KB")
    logger.info(f"   Products: {analysis['data_breakdown']['trending_products']}")
    logger.info(f"   Searches: {analysis['data_breakdown']['search_trends']}")
    logger.info(f"   Brands: {analysis['data_breakdown']['brand_performance']}")
    
    if analysis['sample_data']['top_product']:
        logger.info(f"\n🔥 TOP TRENDING PRODUCT:")
        top_product = analysis['sample_data']['top_product']
        logger.info(f"   {top_product['product_name']} by {top_product['brand']}")
        logger.info(f"   {top_product['search_count']} searches, {top_product['unique_users']} users")
    
    if analysis['sample_data']['top_search']:
        logger.info(f"\n🔍 TOP SEARCH TREND:")
        top_search = analysis['sample_data']['top_search']
        logger.info(f"   '{top_search['query']}' - {top_search['search_count']} searches")
        logger.info(f"   Growth: {top_search['growth_percentage']}%")
    
    if analysis['sample_data']['top_brand']:
        logger.info(f"\n🏆 TOP BRAND:")
        top_brand = analysis['sample_data']['top_brand']
        logger.info(f"   {top_brand['brand']} - {top_brand['total_engagement']} engagement")
        logger.info(f"   {top_brand['unique_users']} users, velocity: {top_brand['trend_velocity']}%")
    
    all_successful = all([
        trending_products_df is not None and len(trending_products_df) > 0,
        search_trends_df is not None and len(search_trends_df) > 0,
        brand_trends_df is not None and len(brand_trends_df) > 0
    ])
    
    if all_successful:
        logger.info(f"\n🎉 DAG VALIDATION SUCCESSFUL!")
        logger.info("   All three analytics components working")
        logger.info("   Complete Supabase payload generated")
        logger.info("   Ready for production deployment!")
    else:
        logger.info(f"\n❌ DAG VALIDATION FAILED!")
        logger.info("   Some components are not working properly")
    
    monthly_storage = analysis['payload_summary']['total_size_kb'] * 30 / 1024
    logger.info(f"\n💾 ESTIMATED MONTHLY STORAGE: {monthly_storage:.1f} MB")

if __name__ == "__main__":
    main()