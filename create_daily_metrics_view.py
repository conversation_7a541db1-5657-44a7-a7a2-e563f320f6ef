#!/usr/bin/env python3
"""
Script to create a simple daily metrics view in Supabase
"""

import os
from supabase import create_client, Client

def main():
    # Load environment variables
    supabase_url = os.getenv("SUPABASE_URL")
    supabase_key = os.getenv("SUPABASE_SERVICE_ROLE_KEY")
    
    if not supabase_url or not supabase_key:
        print("❌ Missing Supabase credentials in .env file")
        return
    
    # Create Supabase client
    supabase: Client = create_client(supabase_url, supabase_key)
    
    print("🗑️ Dropping existing views...")
    
    # Drop existing views
    drop_queries = [
        "DROP VIEW IF EXISTS daily_platform_metrics CASCADE;",
        "DROP VIEW IF EXISTS daily_metrics_comparison CASCADE;"
    ]
    
    for query in drop_queries:
        try:
            result = supabase.postgrest.rpc('exec_sql', {'sql': query}).execute()
            print(f"   ✅ Executed: {query}")
        except Exception as e:
            print(f"   ⚠️ Warning dropping view: {e}")
    
    print("\n📊 Creating new simple daily metrics view...")
    
    # Create simple daily metrics view
    create_view_sql = """
    CREATE VIEW daily_metrics AS
    SELECT 
        DATE(transaction_date) as date,
        
        -- ShopMy metrics (only confirmed transactions)
        ROUND(
            COALESCE(SUM(
                CASE WHEN platform = 'shopmy' AND status = 'confirmed' 
                THEN COALESCE(final_order_amount, order_amount) 
                ELSE 0 END
            ), 0)::numeric, 2
        ) as shopmy_gmv,
        
        ROUND(
            COALESCE(SUM(
                CASE WHEN platform = 'shopmy' AND status = 'confirmed' 
                THEN COALESCE(final_commission_amount, commission_amount) 
                ELSE 0 END
            ), 0)::numeric, 2
        ) as shopmy_revenue,
        
        ROUND(
            CASE 
                WHEN COUNT(CASE WHEN platform = 'shopmy' AND status = 'confirmed' THEN 1 END) > 0 
                THEN SUM(
                    CASE WHEN platform = 'shopmy' AND status = 'confirmed' 
                    THEN COALESCE(final_order_amount, order_amount) 
                    ELSE 0 END
                ) / COUNT(CASE WHEN platform = 'shopmy' AND status = 'confirmed' THEN 1 END)
                ELSE 0
            END::numeric, 2
        ) as shopmy_aov,
        
        -- Strackr metrics (only confirmed transactions)
        ROUND(
            COALESCE(SUM(
                CASE WHEN platform = 'strackr' AND status = 'confirmed' 
                THEN COALESCE(final_order_amount, order_amount) 
                ELSE 0 END
            ), 0)::numeric, 2
        ) as strackr_gmv,
        
        ROUND(
            COALESCE(SUM(
                CASE WHEN platform = 'strackr' AND status = 'confirmed' 
                THEN COALESCE(final_commission_amount, commission_amount) 
                ELSE 0 END
            ), 0)::numeric, 2
        ) as strackr_revenue,
        
        ROUND(
            CASE 
                WHEN COUNT(CASE WHEN platform = 'strackr' AND status = 'confirmed' THEN 1 END) > 0 
                THEN SUM(
                    CASE WHEN platform = 'strackr' AND status = 'confirmed' 
                    THEN COALESCE(final_order_amount, order_amount) 
                    ELSE 0 END
                ) / COUNT(CASE WHEN platform = 'strackr' AND status = 'confirmed' THEN 1 END)
                ELSE 0
            END::numeric, 2
        ) as strackr_aov
        
    FROM normalized_transactions
    GROUP BY DATE(transaction_date)
    ORDER BY date DESC;
    """
    
    try:
        result = supabase.postgrest.rpc('exec_sql', {'sql': create_view_sql}).execute()
        print("   ✅ Created daily_metrics view successfully")
    except Exception as e:
        print(f"   ❌ Error creating view: {e}")
        return
    
    print("\n🔍 Testing the new view...")
    
    # Test the view
    test_query = "SELECT * FROM daily_metrics ORDER BY date DESC LIMIT 10;"
    
    try:
        result = supabase.postgrest.rpc('exec_sql', {'sql': test_query}).execute()
        
        if result.data and len(result.data) > 0:
            print(f"   ✅ View working! Found {len(result.data)} recent days of data")
            print("\n📈 Sample data (last 5 days):")
            print("Date       | ShopMy GMV | ShopMy AOV | ShopMy Rev | Strackr GMV | Strackr AOV | Strackr Rev")
            print("-" * 90)
            
            for row in result.data[:5]:
                date = row.get('date', 'N/A')
                shopmy_gmv = row.get('shopmy_gmv', 0)
                shopmy_aov = row.get('shopmy_aov', 0)
                shopmy_rev = row.get('shopmy_revenue', 0)
                strackr_gmv = row.get('strackr_gmv', 0)
                strackr_aov = row.get('strackr_aov', 0)
                strackr_rev = row.get('strackr_revenue', 0)
                
                print(f"{date} | ${shopmy_gmv:>9} | ${shopmy_aov:>9} | ${shopmy_rev:>9} | ${strackr_gmv:>10} | ${strackr_aov:>10} | ${strackr_rev:>10}")
        else:
            print("   ⚠️ View created but no data returned")
            
    except Exception as e:
        print(f"   ❌ Error testing view: {e}")
    
    print("\n✅ Daily metrics view setup complete!")
    print("💡 You can now query: SELECT * FROM daily_metrics ORDER BY date DESC;")

if __name__ == "__main__":
    main()
