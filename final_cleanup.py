import csv
import re
from difflib import Sequence<PERSON>atch<PERSON>

def normalize_for_matching(name):
    """
    Aggressive normalization for exact matching
    """
    if not name:
        return ""
    
    # Convert to lowercase
    name = name.lower().strip()
    
    # Remove quotes
    name = name.replace('"', '').replace("'", "")
    
    # Remove everything after comma (like ", a division of...")
    name = name.split(',')[0].strip()
    
    # Remove common business suffixes
    suffixes = [
        r'\s*(inc\.?|llc\.?|ltd\.?|corp\.?|corporation|company|co\.?)\s*$',
        r'\s*\([^)]*\)\s*',  # Remove parenthetical content
    ]
    
    for suffix in suffixes:
        name = re.sub(suffix, '', name, flags=re.IGNORECASE)
    
    # Remove "the" prefix
    name = re.sub(r'^\s*the\s+', '', name, flags=re.IGNORECASE)
    
    # Remove special characters except &
    name = re.sub(r'[^\w\s&]', ' ', name)
    name = re.sub(r'\s+', ' ', name).strip()
    
    # Remove geographic indicators
    name = re.sub(r'\s+(us|usa|america|american)\s*$', '', name, flags=re.IGNORECASE)
    
    return name

def final_cleanup():
    """
    Final cleanup with improved matching
    """
    print("Performing final cleanup...")
    
    current_file = "download (19).csv"
    missing_file = "missing_cleaned.csv"
    output_file = "missing_final.csv"
    
    # Read current merchants
    print("Reading current merchants...")
    current_merchants = set()
    
    with open(current_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            name = row.get("advertiser_name", "") or row.get("\ufeffadvertiser_name", "")
            if name:
                normalized = normalize_for_matching(name)
                current_merchants.add(normalized)
    
    print(f"Found {len(current_merchants)} current merchants")
    
    # Filter missing merchants
    print("Filtering missing merchants...")
    final_merchants = []
    removed_count = 0
    
    with open(missing_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            merchant_name = row.get('merchant_name', '').strip()
            if not merchant_name:
                continue
            
            normalized = normalize_for_matching(merchant_name)
            
            if normalized in current_merchants:
                removed_count += 1
                print(f"Removed: {merchant_name} (normalized: {normalized})")
            else:
                final_merchants.append(row)
    
    # Write final results
    print(f"Writing {len(final_merchants)} final merchants to {output_file}...")
    
    with open(output_file, 'w', newline='', encoding='utf-8') as f:
        if final_merchants:
            fieldnames = final_merchants[0].keys()
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(final_merchants)
    
    print(f"\n📊 FINAL CLEANUP SUMMARY:")
    print(f"Input merchants: {removed_count + len(final_merchants):,}")
    print(f"Additional removed: {removed_count:,}")
    print(f"Final merchants: {len(final_merchants):,}")
    
    # Show top final merchants
    print(f"\n🔝 Top 10 final merchants:")
    for i, merchant in enumerate(final_merchants[:10]):
        print(f"  {i+1:2d}. {merchant['merchant_name']:<40} - {merchant['product_count']:>10} products")
    
    print(f"\n✅ Final cleanup complete! Use '{output_file}' as your definitive missing merchants list.")

if __name__ == "__main__":
    final_cleanup()
