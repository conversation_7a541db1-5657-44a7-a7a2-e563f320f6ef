#!/usr/bin/env python3
"""
<PERSON>ript to examine detailed schemas and sample data from key trending product tables
"""

import logging
from google.cloud import bigquery
from google.auth import default
import pandas as pd
import json
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

PROJECT_ID = 'phia-prod-416420'
DATASET_ID = 'mixpanel'

def get_bigquery_client():
    try:
        credentials, _ = default()
        client = bigquery.Client(credentials=credentials, project=PROJECT_ID)
        return client
    except Exception as e:
        logger.error(f"Failed to initialize BigQuery client: {str(e)}")
        raise

def examine_table_structure(client, table_name):
    """Get detailed schema and sample data for a table"""
    full_table_id = f"{PROJECT_ID}.{DATASET_ID}.{table_name}"
    
    try:
        table = client.get_table(full_table_id)
        
        # Get schema details
        schema_details = []
        for field in table.schema:
            schema_details.append({
                'field_name': field.name,
                'field_type': field.field_type,
                'mode': field.mode,
                'description': field.description or ''
            })
        
        # Get sample data with all columns
        sample_query = f"""
        SELECT *
        FROM `{full_table_id}`
        ORDER BY time DESC  -- Assuming most tables have a 'time' field
        LIMIT 5
        """
        
        try:
            sample_results = client.query(sample_query).result()
            sample_data = []
            
            for row in sample_results:
                row_dict = dict(row)
                # Convert complex types to strings for readability
                row_str = {}
                for key, value in row_dict.items():
                    if value is None:
                        row_str[key] = None
                    elif isinstance(value, (dict, list)):
                        row_str[key] = json.dumps(value, default=str)
                    else:
                        row_str[key] = str(value) if len(str(value)) <= 200 else str(value)[:200] + "..."
                sample_data.append(row_str)
                
        except Exception as e:
            # Try without ORDER BY time in case time field doesn't exist
            sample_query = f"SELECT * FROM `{full_table_id}` LIMIT 5"
            try:
                sample_results = client.query(sample_query).result()
                sample_data = [dict(row) for row in sample_results]
            except Exception as e2:
                logger.warning(f"Could not get sample data for {table_name}: {str(e2)}")
                sample_data = []
        
        return {
            'table_name': table_name,
            'row_count': table.num_rows,
            'schema': schema_details,
            'sample_data': sample_data
        }
        
    except Exception as e:
        logger.error(f"Error examining table {table_name}: {str(e)}")
        return None

def main():
    try:
        logger.info("Starting detailed table schema examination...")
        client = get_bigquery_client()
        
        # Key tables we need to understand in detail
        key_tables = [
            # Core engagement tables (highest priority)
            'user_searched_products',
            'product_result_shown', 
            'text_search',
            'product_result_clicked',
            'top_results_clicked',
            'item_favorited',
            'search_success',
            
            # Secondary tables
            'explore_product_opened',
            'view_similar_items_clicked',
            'product_favorited',
            'brand_search',
            'landing_page_popular_search_item_clicked'
        ]
        
        all_table_info = []
        
        for i, table_name in enumerate(key_tables, 1):
            logger.info(f"Examining table {i}/{len(key_tables)}: {table_name}")
            table_info = examine_table_structure(client, table_name)
            
            if table_info:
                all_table_info.append(table_info)
                
                # Print immediate insights
                print(f"\n{'='*60}")
                print(f"TABLE: {table_name}")
                print(f"Rows: {table_info['row_count']:,}")
                print(f"Columns: {len(table_info['schema'])}")
                print("\nSchema:")
                for field in table_info['schema']:
                    print(f"  - {field['field_name']} ({field['field_type']}) - {field['description']}")
                
                if table_info['sample_data']:
                    print(f"\nSample data keys: {list(table_info['sample_data'][0].keys())}")
        
        # Save detailed analysis
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"table_schemas_detailed_{timestamp}.json"
        
        with open(output_file, 'w') as f:
            json.dump(all_table_info, f, indent=2, default=str)
        
        logger.info(f"Detailed schema analysis saved to: {output_file}")
        print(f"\nDetailed analysis saved to: {output_file}")
        
    except Exception as e:
        logger.error(f"Schema examination failed: {str(e)}", exc_info=True)

if __name__ == "__main__":
    main()