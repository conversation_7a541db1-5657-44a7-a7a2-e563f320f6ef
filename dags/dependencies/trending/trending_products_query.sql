-- Trending Products Analytics Query
-- Calculates trending scores for products based on views, clicks, and favorites
-- Parameters: {{ execution_date }}, {{ lookback_days }}

WITH product_metrics AS (
  -- Get all product engagement in the past N days
  SELECT
    JSON_EXTRACT_SCALAR(scraped_data, '$.product_id') as product_id,
    JSON_EXTRACT_SCALAR(scraped_data, '$.url') as product_url,
    JSON_EXTRACT_SCALAR(scraped_data, '$.brand') as brand,
    JSON_EXTRACT_SCALAR(scraped_data, '$.name') as product_name,
    CAST(JSON_EXTRACT_SCALAR(scraped_data, '$.price') AS FLOAT64) as price,
    DATE(time) as event_date,
    mp_country_code,
    mp_city,
    platform,
    'shown' as event_type,
    1 as weight
  FROM `{{ project_id }}.{{ mixpanel_dataset }}.product_result_shown`
  WHERE DATE(time) BETWEEN DATE_SUB(DATE('{{ execution_date }}'), INTERVAL {{ lookback_days }} DAY) 
    AND DATE('{{ execution_date }}')
    AND JSON_EXTRACT_SCALAR(scraped_data, '$.product_id') IS NOT NULL
  
  UNION ALL
  
  SELECT
    product_id,
    product_url,
    NULL as brand,
    NULL as product_name,
    NULL as price,
    DATE(time) as event_date,
    mp_country_code,
    mp_city,
    platform,
    'clicked' as event_type,
    3 as weight -- Clicks worth 3x views
  FROM `{{ project_id }}.{{ mixpanel_dataset }}.product_result_clicked`
  WHERE DATE(time) BETWEEN DATE_SUB(DATE('{{ execution_date }}'), INTERVAL {{ lookback_days }} DAY) 
    AND DATE('{{ execution_date }}')
    AND product_id IS NOT NULL
  
  UNION ALL
  
  SELECT
    product_id,
    NULL as product_url,
    NULL as brand,
    NULL as product_name,
    NULL as price,
    DATE(time) as event_date,
    mp_country_code,
    mp_city,
    platform,
    'favorited' as event_type,
    5 as weight -- Favorites worth 5x views
  FROM `{{ project_id }}.{{ mixpanel_dataset }}.item_favorited`
  WHERE DATE(time) BETWEEN DATE_SUB(DATE('{{ execution_date }}'), INTERVAL {{ lookback_days }} DAY) 
    AND DATE('{{ execution_date }}')
    AND product_id IS NOT NULL
),

aggregated_metrics AS (
  SELECT
    product_id,
    ANY_VALUE(product_url) as product_url,
    ANY_VALUE(brand) as brand,
    ANY_VALUE(product_name) as product_name,
    AVG(price) as avg_price,
    
    -- Engagement metrics
    SUM(CASE WHEN event_type = 'shown' THEN weight ELSE 0 END) as impressions,
    SUM(CASE WHEN event_type = 'clicked' THEN weight ELSE 0 END) as click_score,
    SUM(CASE WHEN event_type = 'favorited' THEN weight ELSE 0 END) as favorite_score,
    
    -- Recency boost (more recent = higher score)
    SUM(weight * (1 + (DATE_DIFF(event_date, DATE_SUB(DATE('{{ execution_date }}'), 
        INTERVAL {{ lookback_days }} DAY), DAY) / {{ lookback_days }}))) as recency_weighted_score,
    
    -- Velocity (compare last 3 days vs previous period)
    SUM(CASE WHEN event_date >= DATE_SUB(DATE('{{ execution_date }}'), INTERVAL 3 DAY) 
        THEN weight ELSE 0 END) as recent_engagement,
    SUM(CASE WHEN event_date < DATE_SUB(DATE('{{ execution_date }}'), INTERVAL 3 DAY) 
        THEN weight ELSE 0 END) as previous_engagement,
        
    -- Geographic distribution
    APPROX_TOP_COUNT(mp_country_code, 3) as top_countries,
    COUNT(DISTINCT mp_country_code) as country_diversity,
    
    -- Platform breakdown
    COUNTIF(platform = 'iOS') as ios_engagement,
    COUNTIF(platform = 'Web') as web_engagement,
    COUNTIF(platform = 'extension') as extension_engagement
    
  FROM product_metrics
  GROUP BY product_id
  HAVING impressions > 10 -- Minimum threshold
),

scored_products AS (
  SELECT
    product_id,
    product_url,
    brand,
    product_name,
    avg_price,
    impressions,
    click_score,
    favorite_score,
    
    -- Calculate trending score
    recency_weighted_score + 
    (SAFE_DIVIDE(recent_engagement - previous_engagement, 
                 NULLIF(previous_engagement, 0)) * 100) as trending_score,
    
    -- Additional metrics
    SAFE_DIVIDE(click_score, impressions) as click_rate,
    SAFE_DIVIDE(favorite_score, impressions) as favorite_rate,
    
    -- Geographic and platform data
    top_countries,
    country_diversity,
    STRUCT(
      ios_engagement,
      web_engagement,
      extension_engagement
    ) as platform_breakdown
    
  FROM aggregated_metrics
)

SELECT 
  ROW_NUMBER() OVER (ORDER BY trending_score DESC) as rank,
  product_id,
  product_url,
  IFNULL(brand, 'Unknown') as brand,
  IFNULL(product_name, 'Unknown Product') as product_name,
  ROUND(avg_price, 2) as price,
  ROUND(trending_score, 2) as score,
  impressions,
  ROUND(click_rate * 100, 2) as ctr_percentage,
  ROUND(favorite_rate * 100, 2) as favorite_percentage,
  TO_JSON_STRING(top_countries) as top_countries_json,
  country_diversity,
  TO_JSON_STRING(platform_breakdown) as platform_breakdown_json
FROM scored_products
ORDER BY trending_score DESC
LIMIT 100